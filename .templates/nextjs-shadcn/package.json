{"name": "nextjs-shadcn", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -H 0.0.0.0 --turbopack", "build": "bun run db:generate && next build", "start": "next start -H 0.0.0.0", "db:generate": "bunx prisma generate || echo 'Prisma not configured, skipping...'", "db:push": "bunx prisma db push || echo 'Prisma not configured'", "db:migrate": "bunx prisma migrate dev || echo 'Prisma not configured'", "lint": "bunx biome lint --write && bunx tsc --noEmit", "format": "bunx biome format --write", "test": "jest", "postinstall": "bun run db:generate"}, "dependencies": {"@radix-ui/react-slot": "^1.2.3", "@types/supertest": "^6.0.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dayjs": "^1.11.13", "jest": "^30.0.2", "lodash": "^4.17.21", "lucide-react": "^0.475.0", "next": "^15.3.2", "pdf-parse": "^1.1.1", "react": "^18.3.1", "react-dom": "^18.3.1", "supertest": "^7.1.1", "tailwind-merge": "^3.3.0", "tailwindcss-animate": "^1.0.7", "vercel": "^44.2.5"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@eslint/eslintrc": "^3.3.1", "@types/bcryptjs": "^3.0.0", "@types/node": "^20.17.50", "@types/react": "^18.3.22", "@types/react-dom": "^18.3.7", "eslint": "^9.27.0", "eslint-config-next": "15.1.7", "jest-environment-jsdom": "^30.0.2", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "typescript": "^5.8.3", "undici": "^7.10.0"}}