/* Animation Utilities */

/* Animation Keyframes */
@keyframes shimmer {
  100% {
    transform: translateX(100%);
  }
}

@keyframes dot-bounce {
  0%, 100% {
    transform: translateY(0);
    opacity: 0.5;
  }
  50% {
    transform: translateY(-4px);
    opacity: 1;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
    transform: translateY(0);
  }
  to {
    opacity: 0;
    transform: translateY(10px);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Animation Utility Classes */
@layer components {
  /* Fade Animations */
  .animate-fadeIn {
    animation: fadeIn 0.3s ease-out;
  }
  
  .animate-fadeOut {
    animation: fadeOut 0.3s ease-out;
  }
  
  /* Slide Animations */
  .animate-slideInLeft {
    animation: slideInLeft 0.3s ease-out;
  }
  
  .animate-slideInRight {
    animation: slideInRight 0.3s ease-out;
  }
  
  /* Scale Animations */
  .animate-scaleIn {
    animation: scaleIn 0.2s ease-out;
  }
  
  /* Loading Animations */
  .animate-shimmer {
    position: relative;
    overflow: hidden;
  }
  
  .animate-shimmer::before {
    content: "";
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.2),
      transparent
    );
    transform: translateX(-100%);
    animation: shimmer 2s infinite;
  }
  
  .animate-dot-bounce > * {
    animation: dot-bounce 1.4s infinite;
  }
  
  .animate-dot-bounce > *:nth-child(2) {
    animation-delay: 0.2s;
  }
  
  .animate-dot-bounce > *:nth-child(3) {
    animation-delay: 0.4s;
  }
  
  .animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }
  
  .animate-spin {
    animation: spin 1s linear infinite;
  }
  
  /* Animation Modifiers */
  .animation-delay-100 {
    animation-delay: 100ms;
  }
  
  .animation-delay-200 {
    animation-delay: 200ms;
  }
  
  .animation-delay-300 {
    animation-delay: 300ms;
  }
  
  .animation-delay-500 {
    animation-delay: 500ms;
  }
  
  .animation-duration-200 {
    animation-duration: 200ms;
  }
  
  .animation-duration-300 {
    animation-duration: 300ms;
  }
  
  .animation-duration-500 {
    animation-duration: 500ms;
  }
  
  .animation-duration-700 {
    animation-duration: 700ms;
  }
  
  .animation-duration-1000 {
    animation-duration: 1000ms;
  }
}